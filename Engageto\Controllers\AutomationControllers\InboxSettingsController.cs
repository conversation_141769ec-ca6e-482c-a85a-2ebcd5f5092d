﻿using EngagetoContracts.AutomationContracts;
using EngagetoContracts.Services;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.AutomationDtos;
using EngagetoEntities.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace Engageto.Controllers.AutomationControllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InboxSettingsController : BaseController
    {
        private readonly IRepositoryBase context;
        private readonly ApplicationDbContext  _applicationDbContext;
        private readonly IAutomationSettingService _automationSettingService;
        private readonly IUserIdentityService _userIdentityService;
        public InboxSettingsController(IRepositoryBase repository, ApplicationDbContext Db,
            IAutomationSettingService automationSettingService, IUserIdentityService userIdentityService)
        {
            context = repository;
            _applicationDbContext = Db;
            _automationSettingService = automationSettingService;
            _userIdentityService = userIdentityService;
        }
        [Route("/api/update_inboxsettings")]
        [HttpPost]
        [Authorize]
        public async Task<ActionResult> InboxSettings([FromQuery, Required] Guid UserId, [FromQuery, Required] Guid BusinessId, InboxSettingsMessageDto inboxSettingsMessageDto)
        {
            try
            {
                await context.InboxSettingsMessage(UserId, BusinessId, inboxSettingsMessageDto);
                return Ok(new { Message = $"{inboxSettingsMessageDto.Feature.ToString()} updated successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [Route("/api/get_inboxsettings")]
        [HttpGet]
        [Authorize]
        public async Task<ActionResult> GetInboxSettings([FromQuery, Required] Guid UserId, [FromQuery, Required] Guid BusinessId, Feature feature)
        {
            try
            {
                var Data = _applicationDbContext.Users.Where(m => m.Status).Select(m => m.Id).ToArray();
                if (!(Data.Contains(UserId)))
                {
                    return BadRequest(new { Message = "Please check User details credentials." });
                }
                var data = await context.GetInboxSettingsMessage(UserId, BusinessId, feature);
                return Ok(data);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("automation-setting")]
        [Authorize]
        public async Task<IActionResult> SaveAutomationSetting(BaseAutomationSettingDto settingDto)
        {
            try
            {
                var result = await _automationSettingService.SaveAutomationSettingAsync(settingDto);
                return Ok(CreateSuccessResponse<int>(result, "Save automation setting"));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                return BadRequest(error);
            }
        }
        [HttpPut("automation-setting")]
        [Authorize]
        public async Task<IActionResult> UpdateAutomationSetting(EditAutomationSettingDto settingDto)
        {
            try
            {
                var result = await _automationSettingService.UpdateAutomationSettingAsync(settingDto);
                return Ok(CreateSuccessResponse<ViewAutomationSettingDto>(result, "update automation setting"));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                return BadRequest(error);
            }
        }
        [HttpGet("automation-setting")]
        [Authorize]
        public async Task<IActionResult> GetAutomationSetting()
        {
            try
            {
                var businessId = _userIdentityService.BusinessId;
                var result = await _automationSettingService.GetAutomationSettingAsync(businessId);
                return Ok(CreateSuccessResponse<ViewAutomationSettingDto>(result, "automation setting"));
            }
            catch (Exception ex)
            {
                var error = CreateErrorResponse<string>(ex.Message, ex.StackTrace);
                return BadRequest(error);
            }
        }
    }
}
