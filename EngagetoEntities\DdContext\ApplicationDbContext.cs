﻿using EngagetoEntities.Entities;
using EngagetoEntities.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace EngagetoEntities.DdContext
{
    public class ApplicationDbContext : DbContext
    {
        private readonly ConnectionString _conn;
        private readonly ILoggerFactory _loggerFactory;
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options,
                            IOptions<ConnectionString> conn,
                            ILoggerFactory loggerFactory)
            : base(options)
        {
            _conn = conn.Value ?? throw new ArgumentNullException(nameof(conn));
            _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder
                .UseLoggerFactory(_loggerFactory) // Use the application's logger
                .UseSqlServer(_conn.ConnStr, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 5,
                        maxRetryDelay: TimeSpan.FromSeconds(10),
                        errorNumbersToAdd: null);
                }).EnableSensitiveDataLogging(); // Be cautious with this in production;
        }


        #region Templates and Buttons
        public DbSet<Template> Templates { get; set; }
        public DbSet<Button> ButtonDetails { get; set; }
        #endregion

        #region Business Details and Users
        public DbSet<BusinessDetailsMeta> BusinessDetailsMetas { get; set; }
        public DbSet<Ahex_CRM_Users> Users { get; set; }
        public DbSet<Role> Role { get; set; }
        public DbSet<CountryDetails> CountryDetails { get; set; }
        public DbSet<Contacts> Contacts { get; set; }
        public DbSet<Ahex_CRM_BusinessDetails> BusinessDetails { get; set; }
        #endregion

        #region Conversations and Chat Management
        public DbSet<Conversations> Conversations { get; set; }
        public DbSet<ChatStatusEntity> ChatStatusEntities { get; set; }
        public DbSet<Notes> Notes { get; set; }
        public DbSet<Tags> Tags { get; set; }
        public DbSet<OptInManagement> OptInManagement { get; set; }
        #endregion

        #region Campaigns and Automations
        public DbSet<Campaign> Campaigns { get; set; }
        public DbSet<CampaignTracker> CampaignTracker { get; set; }
        public DbSet<WorkingHours> WorkingHours { get; set; }
        public DbSet<InboxSettingsVariable> InboxSettingsVariables { get; set; }
        public DbSet<AutomationSetting> AutomationSettings { get; set; }
        public DbSet<AutoReplyAutomationEntity> AutoReplyAutomationEntities { get; set; }
        public DbSet<AutoReplyCustomMessageEntity> AutoReplyCustomMessageEntities { get; set; }
        public DbSet<AutoReplyVeriableEntity> AutoReplyVeriableEntities { get; set; }
        public DbSet<AutomationSelectResponseEntity> AutomationSelectResponseEntities { get; set; }
        public DbSet<WorkflowResponseHistoryEntity> WorkflowResponseHistoryEntities { get; set; }
        public DbSet<AutomationCustomerResponseEntity> AutomationCustomerResponseEntities { get; set; }
        #endregion

        #region Workflow and Variables
        public DbSet<VeriableEntity> VeriableEntities { get; set; }
        public DbSet<WorkflowEntity> WorkflowEntities { get; set; }
        public DbSet<VeriableNameEntity> VeriableNameEntities { get; set; }
        public DbSet<InputListEntity> InputListEntities { get; set; }
        public DbSet<Workflow> Workflows { get; set; }
        public DbSet<WorkflowNode> WorkflowNodes { get; set; }
        public DbSet<WorkflowEdge> WorkflowEdges { get; set; }
        public DbSet<WorkflowEdgeTargetNode> WorkflowEdgeTargetNodes { get; set; }
        #endregion

        #region Widget Management
        public DbSet<WidgetEntity> WidgetEntities { get; set; }
        public DbSet<WidgetUrlFieldEntity> WidgetUrlFieldEntities { get; set; }
        #endregion

        #region Filters and Meta Pricing
        public DbSet<Filters> Filters { get; set; }
        public DbSet<MasterMetaPrice> MasterMetaPrice { get; set; }
        public DbSet<CustomMetaApiFeeInfo> CustomMetaApiFeeInfo { get; set; }
        #endregion

        #region API and Integrations
        public DbSet<ApiKeyEntity> ApiKeyEntities { get; set; }
        public DbSet<IntegrationAccount> IntegrationAccounts { get; set; }
        #endregion

        #region File and Logs
        public DbSet<UploadedFile> UploadedFiles { get; set; }
        public DbSet<LogHistoryEntitity> LogHistoryEntities { get; set; }
        public DbSet<ContactImportTracker> ContactImportTrackers { get; set; }
        #endregion

        #region Analytics and Pricing
        public DbSet<ConversationAnalyticsEntity> ConversationAnalyticsEntities { get; set; }
        public DbSet<ConversationAnalyticsJobRequest> ConversationAnalyticsJobRequest { get; set; }
        public DbSet<ConversationAnalyticsPriceEntity> ConversationAnalyticsPriceEntities { get; set; }
        public DbSet<ConversationCostDetuctionHistory> ConversationCostDetuctionHistory { get; set; }
        public DbSet<DiscountEntity> DiscountEntities { get; set; }
        #endregion

        #region Roles and Permissions
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<AssignPermissions> AssignPermissionsToRoleIds { get; set; }
        public DbSet<RolePermissions> RolePermissions { get; set; }
        public DbSet<MenuDetails> MenuDetails { get; set; }
        public DbSet<MenuwithRoleRelationDetails> MenuwithRoleRelationDetails { get; set; }
        public DbSet<Permissions> Permissions { get; set; }
        #endregion

        #region Authentication and User Management
        public DbSet<HelpCenter> HelpCenters { get; set; }
        public DbSet<ForgotPassword> ForgotPasswords { get; set; }
        #endregion

        #region Plans, Payments, and Wallet
        public DbSet<PlanEntities> PlanEntities { get; set; }
        public DbSet<PaymentWalletDetail> PaymentWalletDetails { get; set; }
        public DbSet<UserWalletEntity> UserWalletEntities { get; set; }
        public DbSet<TransactionHistoryEntity> TransactionHistoryEntities { get; set; }
        public DbSet<Subscriptions> Subscriptions { get; set; }
        #endregion

        #region Notifications and Environment
        public DbSet<WalletNotification> WalletNotificationEntities { get; set; }
        public DbSet<NotificationEntities> NotificationEntities { get; set; }
        public DbSet<UserNotificationEntities> UserNotificationEntities { get; set; }
        public DbSet<EnvironmentUrlEntity> EnvironmentUrlEntities { get; set; }
        public DbSet<FirebaseDeviceRegistration> FirebaseDeviceRegistrations { get; set; }
        #endregion

        public DbSet<CustomerWorkflowTracker> CustomerWorkflowTrackers { get; set; }
        public DbSet<WorkflowKeyword> WorkflowKeywords { get; set; }
        public DbSet<AttributeNameEntity> AttributeNameEntity { get; set; }
        public DbSet<WorkflowCustomerResponse> WorkflowCustomerResponse { get; set; }
    }
}
