using EngagetoBackGroundJobs.Implementation;
using EngagetoBackGroundJobs.Interfaces;
using EngagetoContracts.GeneralContracts;
using EngagetoContracts.MetaContracts;
using EngagetoContracts.Services;
using EngagetoContracts.Workflow;
using EngagetoRepository.GeneralServices;
using EngagetoRepository.MetaServices;
using EngagetoRepository.Services;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;
using EngagetoContracts.AttributeName;
using EngagetoContracts.WorkflowRepository;
using EngagetoRepository.Repostiories;
using EngagetoContracts.TemplateContracts;
using EngagetoRepository.TemplateRepository;
using EngagetoContracts.UserContracts;
using EngagetoRepository.UserRepository;
using EngagetoContracts.ContactContracts;
using EngagetoContracts.WebhookContracts.DownloadMedia;
using EngagetoContracts.WebhookContracts.SentMessage;
using EngagetoEntities.Validations.TemplateValidation;
using EngagetoRepository.ContactsRepository;
using EngagetoRepository.Repository;
using EngagetoRepository.WebhookRepository.DownloadMediaRepo;
using EngagetoRepository.WebhookRepository.SentMessageRepo;
using EngagetoContracts.CampaignContracts;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoContracts.WebhookContracts.ReceivedNotification;
using EngagetoDatabase.WebhookRepository.ReceiveTemplateRepo;
using EngagetoRepository.CampaignRepository;
using EngagetoRepository.WebhookRepository.ReceivedMessageRepo;
using EngagetoRepository.WebhookRepository.SentMessageService;
using Engageto.Hubs;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using EngagetoDapper.Data.Connections;
using EngagetoDapper;
using EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserRepositories;
using EngagetoDapper.Data.Dapper.Repositories.UserRepositories;


var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
       .ConfigureAppConfiguration((context, config) =>
       {
           config.SetBasePath(Directory.GetCurrentDirectory())
               .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
               .AddEnvironmentVariables();
       })
    .ConfigureServices((context, services) =>
    {
        // Add Application Insights
        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        // Add Configuration
        services.AddSingleton<IConfiguration>(context.Configuration);

        var connectionString = context.Configuration["ConnStr"];

        // Configure ConnectionString options for ApplicationDbContext
        services.Configure<EngagetoEntities.Settings.ConnectionString>(options =>
        {
            options.ConnStr = connectionString;
        });

        // Add LoggerFactory
        services.AddLogging();

        services.AddDbContext<EngagetoEntities.DdContext.ApplicationDbContext>(options =>
               options.UseSqlServer(connectionString));

        services.AddScoped<IDbConnection>(sp =>
        {
            var connection = new SqlConnection(connectionString);
            return connection;
        });

        services.AddScoped<IUnitOfWork>(sp =>
        {
            var dbConnection = sp.GetRequiredService<IDbConnection>();
            // 🔧 CRITICAL FIX: Don't open connection here - let UnitOfWork handle it
            // Connection will be opened when first accessed
            return new UnitOfWork(dbConnection);
        });

        // 🗑️ REMOVED: DapperConnectionFactory - unnecessary complexity
        // Services now use IUnitOfWork directly for better transaction handling

        // Register Services
        services.AddSingleton<ISqlConnectionFactory, SqlConnectionFactory>();
        //services.AddScoped<IGenericRepository, GenericRepository>();
        services.AddScoped<ICampaignScheduler, CampaignScheduler>();
        services.AddScoped<IContactScheduler, ContactScheduler>();
        services.AddScoped<IEnvironmentService>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var environmentService = new EnvironmentService(configuration);

            // Simple environment detection based on configuration
            var configEnvironment = configuration["ASPNETCORE_ENVIRONMENT"];
            // Check if we're in development environment
            var isDevelopment = string.Equals(configEnvironment, "Development", StringComparison.OrdinalIgnoreCase);
            environmentService.IsDevelopment = isDevelopment;
            return environmentService;

          
        });
        services.AddScoped<IJobService, JobService>();
        // services.AddScoped<ILogHistoryService, LogHistoryService>();
        //services.AddScoped<IInboxRepository, InboxRepository>();
        services.AddScoped<IMetaApiService, MetaApiService>();
        services.AddScoped<IMetaPayloadService, MetaPayloadService>();
        services.AddScoped<IUserIdentityService, UserIdentityService>();
        //services.AddScoped<ICampaignRespository, CampaignRespository>();
        services.AddScoped<IBlobStorageService, BlobStorageService>();
        services.AddScoped<INodeWorkflowEngineService, NodeWorkflowEngineService>();
        services.AddScoped<IWorkflowCustomResponseService, WorkflowCustomResponseService>();
        services.AddScoped<IAttributeNameService, AttributeNameService>();
        services.AddScoped<ICustomerWorkflowTrackerRepository, CustomerWorkflowTrackerRepository>();

        services.AddScoped<ITemplate, TemplatesService>();
        services.AddScoped<IAccountDetailsService, AccountDetailsService>();
        services.AddScoped<IMediaURL, MediaURL>();
        services.AddScoped<ICompanyDetailsService, CompanyDetailsService>();
        services.AddScoped<IContactRepositoryBase, ContactRepositoryBase>();
        services.AddScoped<IWhatsAppBusinessNotificarion, SentMessageStatusUpdate>();
        services.AddScoped<EngagetoContracts.UserContracts.IEmailService, EmailService>();
        services.AddScoped<TemplateValidation>();


        services.AddScoped<IDownloadMedia, DownloadMeidaService>();
        services.AddScoped<IWhatsAppReceiveNotification, ReceiveMessageService>();
        services.AddScoped<IWhatsAppBusinessClient, SentMessageService>();
        services.AddScoped<ISentMessage, SentMessage>();
        services.AddScoped<TemplateStatus, ReceiveTemplateStatusUpdate>();
        services.AddScoped<IReceivedContacts, ReceivedContacts>();
        services.AddScoped<ICampaign, CampaignRepositories>();
        services.AddScoped<IConversationService, ConversationRepository>();
        services.AddScoped<IWAWebhookMessageServiceAsync, WAWebhookMessageServiceAsync>();
        services.AddScoped<IWorkflowService, WorkflowService>();
        services.AddScoped<IUserRepository, UserRepository>();



        services.AddInfraStructure(context.Configuration);

        services.AddLogging(loggingBuilder =>
        {
            loggingBuilder.AddConsole();
            loggingBuilder.AddDebug();
        });

        // 🚀 OPTIMIZED HTTP CLIENT CONFIGURATION for high-throughput WhatsApp messaging
        services.AddHttpClient("LongRunning", client =>
        {
            client.Timeout = TimeSpan.FromMinutes(10); // Extended timeout for batch operations
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
        {
            MaxConnectionsPerServer = 100, // Increased connection pool
            UseCookies = false // Disable cookies for better performance
        });


        services.AddSignalR();
        services.AddScoped<IWhatsAppBusinessNotificarion, SentMessageStatusUpdate>();
        //services.AddScoped<, SentMessageStatusUpdate>();

        services.AddCors(options =>
        {
            options.AddPolicy("AllowAllOrigins", builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            });
        });
    })
    .Build();

host.Run();