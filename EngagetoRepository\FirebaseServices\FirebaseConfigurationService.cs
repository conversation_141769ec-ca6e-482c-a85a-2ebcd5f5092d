using EngagetoContracts.FirebaseContracts;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EngagetoRepository.FirebaseServices
{
    public class FirebaseConfigurationService : IFirebaseConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<FirebaseConfigurationService> _logger;
        private FirebaseApp? _firebaseApp;
        private bool _isInitialized = false;

        public FirebaseConfigurationService(IConfiguration configuration, ILogger<FirebaseConfigurationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<bool> InitializeFirebaseAsync()
        {
            try
            {
                if (_isInitialized && _firebaseApp != null)
                {
                    return true;
                }

                var firebaseConfig = _configuration.GetSection("Firebase");
                var firebaseSettingConfig = _configuration.GetSection("FirebaseSetting");
                var projectId = firebaseConfig["ProjectId"] ?? firebaseSettingConfig["project_id"];

                if (string.IsNullOrEmpty(projectId))
                {
                    _logger.LogError("Firebase ProjectId is not configured");
                    return false;
                }

                GoogleCredential credential;

                // Try to load credentials from FirebaseSetting configuration
                if (firebaseSettingConfig.Exists())
                {
                    try
                    {
                        // Build Firebase credentials JSON from configuration
                        var firebaseCredentials = new
                        {
                            type = firebaseSettingConfig["type"],
                            project_id = firebaseSettingConfig["project_id"],
                            private_key_id = firebaseSettingConfig["private_key_id"],
                            private_key = firebaseSettingConfig["private_key"],
                            client_email = firebaseSettingConfig["client_email"],
                            client_id = firebaseSettingConfig["client_id"],
                            auth_uri = firebaseSettingConfig["auth_uri"],
                            token_uri = firebaseSettingConfig["token_uri"],
                            auth_provider_x509_cert_url = firebaseSettingConfig["auth_provider_x509_cert_url"],
                            client_x509_cert_url = firebaseSettingConfig["client_x509_cert_url"],
                            universe_domain = firebaseSettingConfig["universe_domain"]
                        };

                        var firebaseSettingJson = System.Text.Json.JsonSerializer.Serialize(firebaseCredentials);

                        _logger.LogInformation("Attempting to load Firebase credentials from FirebaseSetting configuration");
                        _logger.LogDebug("JSON content length: {Length}", firebaseSettingJson.Length);

                        credential = GoogleCredential.FromJson(firebaseSettingJson);
                        _logger.LogInformation("Firebase credentials loaded from FirebaseSetting configuration successfully");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to load Firebase credentials from FirebaseSetting configuration");
                        return false;
                    }
                }
                else
                {
                    // Try to use default credentials (for production environments)
                    try
                    {
                        credential = GoogleCredential.GetApplicationDefault();
                        _logger.LogInformation("Firebase credentials loaded from application default");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to load Firebase credentials. No valid credential source found");
                        return false;
                    }
                }

                var options = new AppOptions()
                {
                    Credential = credential,
                    ProjectId = projectId
                };

                // Check if a default Firebase app already exists
                try
                {
                    _firebaseApp = FirebaseApp.DefaultInstance;
                    if (_firebaseApp != null)
                    {
                        _logger.LogInformation("Using existing Firebase app instance");
                        _isInitialized = true;
                        return true;
                    }
                }
                catch (InvalidOperationException)
                {
                    // No default instance exists, create a new one
                    _logger.LogInformation("Creating new Firebase app instance");
                }

                _firebaseApp = FirebaseApp.Create(options);
                _isInitialized = true;

                _logger.LogInformation("Firebase initialized successfully for project: {ProjectId}", projectId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Firebase");
                return false;
            }
        }

        public FirebaseApp GetFirebaseApp()
        {
            if (!_isInitialized || _firebaseApp == null)
            {
                throw new InvalidOperationException("Firebase is not initialized. Call InitializeFirebaseAsync first.");
            }

            return _firebaseApp;
        }

        public bool IsFirebaseInitialized()
        {
            return _isInitialized && _firebaseApp != null;
        }

        public string GetProjectId()
        {
            var projectId = _configuration["Firebase:ProjectId"] ?? _configuration["FirebaseSetting:project_id"];
            if (string.IsNullOrEmpty(projectId))
            {
                throw new InvalidOperationException("Firebase ProjectId is not configured");
            }
            return projectId;
        }

        public async Task<bool> TestCredentialsAsync()
        {
            try
            {
                var firebaseSettingConfig = _configuration.GetSection("FirebaseSetting");

                if (!firebaseSettingConfig.Exists())
                {
                    _logger.LogError("No FirebaseSetting configuration found");
                    return false;
                }

                _logger.LogInformation("Testing Firebase credentials...");

                // Build Firebase credentials JSON from configuration
                var firebaseCredentials = new
                {
                    type = firebaseSettingConfig["type"],
                    project_id = firebaseSettingConfig["project_id"],
                    private_key_id = firebaseSettingConfig["private_key_id"],
                    private_key = firebaseSettingConfig["private_key"],
                    client_email = firebaseSettingConfig["client_email"],
                    client_id = firebaseSettingConfig["client_id"],
                    auth_uri = firebaseSettingConfig["auth_uri"],
                    token_uri = firebaseSettingConfig["token_uri"],
                    auth_provider_x509_cert_url = firebaseSettingConfig["auth_provider_x509_cert_url"],
                    client_x509_cert_url = firebaseSettingConfig["client_x509_cert_url"],
                    universe_domain = firebaseSettingConfig["universe_domain"]
                };

                var firebaseSettingJson = System.Text.Json.JsonSerializer.Serialize(firebaseCredentials);
                _logger.LogDebug("Credentials JSON length: {Length}", firebaseSettingJson.Length);

                _logger.LogInformation("JSON validation passed");

                var credential = GoogleCredential.FromJson(firebaseSettingJson);
                _logger.LogInformation("GoogleCredential created successfully");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Firebase credentials test failed: {Message}", ex.Message);
                return false;
            }
        }

        private static bool IsBase64String(string base64)
        {
            try
            {
                Convert.FromBase64String(base64);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static string ValidateAndCleanJson(string jsonContent)
        {
            try
            {
                // Remove any BOM or invisible characters
                jsonContent = jsonContent.Trim('\uFEFF', '\u200B', '\0', ' ', '\t', '\r', '\n');

                // Ensure the JSON starts and ends with proper braces
                if (!jsonContent.StartsWith("{"))
                {
                    throw new ArgumentException("JSON content must start with '{'");
                }

                if (!jsonContent.EndsWith("}"))
                {
                    throw new ArgumentException("JSON content must end with '}'");
                }

                // Try to parse and reformat the JSON to ensure it's valid
                using (var document = JsonDocument.Parse(jsonContent))
                {
                    // Validate required fields for Firebase service account
                    var root = document.RootElement;

                    if (!root.TryGetProperty("type", out var typeProperty) ||
                        typeProperty.GetString() != "service_account")
                    {
                        throw new ArgumentException("JSON must be a service account credential");
                    }

                    var requiredFields = new[] { "project_id", "private_key_id", "private_key", "client_email", "client_id" };
                    foreach (var field in requiredFields)
                    {
                        if (!root.TryGetProperty(field, out _))
                        {
                            throw new ArgumentException($"Missing required field: {field}");
                        }
                    }

                    // Return the original content if validation passes
                    return jsonContent;
                }
            }
            catch (JsonException ex)
            {
                throw new ArgumentException($"Invalid JSON format: {ex.Message}", ex);
            }
        }
    }
}
