﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>24aa3a8b-6aa9-484c-aa73-c4bcb3f25403</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net" Version="0.1.0" />
    <PackageReference Include="Concentus.OggFile.NetStandard" Version="1.0.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="FirebaseAdmin" Version="2.4.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNet.SignalR.Core" Version="2.4.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="7.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="7.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.Server.Kestrel" Version="2.1.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="7.0.20" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Razorpay" Version="3.1.2" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EngagetoBackGroundJobs\EngagetoBackGroundJobs.csproj" />
    <ProjectReference Include="..\EngagetoContracts\EngagetoContracts.csproj" />
    <ProjectReference Include="..\EngagetoDapper\EngagetoDapper.csproj" />
    <ProjectReference Include="..\EngagetoEntities\EngagetoEntities.csproj" />
    <ProjectReference Include="..\EngagetoRepository\EngagetoRepository.csproj" />
  </ItemGroup>

</Project>
